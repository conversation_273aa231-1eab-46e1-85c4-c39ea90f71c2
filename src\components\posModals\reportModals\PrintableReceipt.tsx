import { forwardRef } from "react";

export interface OrderItem {
    name: string;
    qty: number;
    price: number;
    subtotal: number;
}

export interface PrintableReceiptProps {
    orderItems: OrderItem[];
    subTotal: number;
    surcharge: number;
    orderDiscount: number;
    tax: number;
    billAmount: number;
    paymentMethod: string;
}

const PrintableReceipt = forwardRef<HTMLDivElement, PrintableReceiptProps>(
    (
        {
            orderItems,
            subTotal,
            surcharge,
            orderDiscount,
            tax,
            billAmount,
            paymentMethod,
        },
        ref
    ) => (
        <div
            ref={ref}
            style={{
                width: 424,
                minHeight: 652,
                padding: 24,
                fontFamily: "Arial, sans-serif",
                color: "#000",
            }}
        >
            <div style={{ display: "flex", justifyContent: "space-between", fontSize: 12 }}>
                <span>{new Date().toLocaleString()}</span>
                <span>Order Receipt</span>
            </div>
            <h2 style={{ textAlign: "center", margin: "16px 0" }}>Order Details</h2>
            <table style={{ width: "100%", borderCollapse: "collapse", marginBottom: 16 }}>
                <thead>
                    <tr>
                        <th style={{ border: "1px solid #000", padding: 6 }}>Item Name</th>
                        <th style={{ border: "1px solid #000", padding: 6 }}>Qty</th>
                        <th style={{ border: "1px solid #000", padding: 6 }}>Price</th>
                        <th style={{ border: "1px solid #000", padding: 6 }}>Subtotal</th>
                    </tr>
                </thead>
                <tbody>
                    {orderItems.map((item, idx) => (
                        <tr key={idx}>
                            <td style={{ border: "1px solid #000", padding: 6 }}>{item.name}</td>
                            <td style={{ border: "1px solid #000", padding: 6, textAlign: "center" }}>{item.qty}</td>
                            <td style={{ border: "1px solid #000", padding: 6, textAlign: "right" }}>${item.price.toFixed(2)}</td>
                            <td style={{ border: "1px solid #000", padding: 6, textAlign: "right" }}>${item.subtotal.toFixed(2)}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
            <div style={{ fontSize: 15, marginBottom: 8 }}>
                <div>Sub Total: <span style={{ float: "right" }}>${subTotal.toFixed(2)}</span></div>
                <div>Surcharge: <span style={{ float: "right" }}>${surcharge.toFixed(2)}</span></div>
                <div>Order Discount: <span style={{ float: "right" }}>${orderDiscount.toFixed(2)}</span></div>
                <div>Tax: <span style={{ float: "right" }}>${tax.toFixed(2)}</span></div>
                <div style={{ fontWeight: "bold" }}>Bill Amount: <span style={{ float: "right" }}>${billAmount.toFixed(2)}</span></div>
            </div>
            <div style={{ fontWeight: "bold", marginTop: 16 }}>
                Payment Method: {paymentMethod}
            </div>
        </div>
    )
);

export default PrintableReceipt;