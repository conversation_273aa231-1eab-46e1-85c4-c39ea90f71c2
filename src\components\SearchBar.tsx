import { FiSearch } from "react-icons/fi";

type Props = {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const SearchBar: React.FC<Props> = ({ value, onChange }) => {
  return (
    <div className="relative py-3 px-3 lg:px-4">
      <div className="relative w-full lg:w-[270px]">
        <input
          type="text"
          value={value}
          onChange={onChange}
          placeholder="Search Menu"
          className="w-full py-2 text-base lg:text-lg rounded-full focus:outline-none border border-gray-300 lg:border-gray-200 pl-10 pr-4 lg:pl-12 lg:pr-6"
        />
        <FiSearch className="absolute top-1/2 left-5 lg:left-4 transform -translate-y-1/2 text-gray-400" size={20} color="#19191c" />
      </div>
    </div>
  );
};

export default SearchBar;
