import React from "react";

interface ProductProps {
  name: string;
  price: number | string;
  image: string;
  onClick: () => void;
}

const ProductCard: React.FC<ProductProps> = ({
  name,
  price,
  image,
  onClick,
}) => (
  <div
    onClick={onClick}
    className="cursor-pointer bg-white rounded-md overflow-hidden w-full shadow-sm hover:shadow-md transition-shadow"
  >
    <div className="relative w-full h-44 md:h-48 xl:h-40">
      <img
        src={image}
        alt={name}
        className="w-full h-full object-cover"
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.src = 'fallback-image-url.jpg';
        }}
      />
    </div>
    <div className="p-3 text-center">
      <h3 className="text-[15px] font-semibold mb-1 line-clamp-2">{name}</h3>
      <p className="text-orange-500 font-semibold text-lg">
        ${typeof price === "number" ? price.toFixed(2) : price}
      </p>
    </div>
  </div>
);

export default ProductCard;
