import React, { useState, useRef, useEffect } from 'react';
import { IoClose } from "react-icons/io5";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { format, addDays, subDays, startOfWeek, isBefore, startOfDay } from 'date-fns';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useCreateTableOperatorMutation, useGetSitesQuery, useGetTablesQuery } from '../../store/api/pos/tableapi';
import Swal from 'sweetalert2';

interface NewReservationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: () => void;
}

const NewReservationModal: React.FC<NewReservationModalProps> = ({ isOpen, onClose, onContinue }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const modalRef = useRef<HTMLDivElement>(null);
  const userId = localStorage.getItem("userId") || " "
  const [createTableOperator, { isLoading, error }] = useCreateTableOperatorMutation();
  const { data: sites } = useGetSitesQuery(userId)
  const { data: tables } = useGetTablesQuery(userId)
  const today = startOfDay(new Date());

  const partySizes = [1, 2, 3, 4, 5, 6, 7, '8+'];

  // Generate time options from 9am to 9pm (only full hours)
  const generateTimeOptions = () => {
    const times = [];
    for (let hour = 9; hour <= 21; hour++) {
      const hour12 = hour > 12 ? hour - 12 : hour;
      const period = hour >= 12 ? 'PM' : 'AM';
      const timeString = `${hour12} ${period}`;
      times.push(timeString);
    }
    return times;
  };

  const timeOptions = generateTimeOptions();

  // Form validation schema
  const validationSchema = Yup.object({
    partySize: Yup.mixed().required('Party size is required'),
    selectedDate: Yup.date()
      .required('Date is required')
      .test('is-future-date', 'Cannot select a past date',
        (value) => !isBefore(startOfDay(value), today)),
    selectedTime: Yup.string()
      .notOneOf(['', 'Select time'], 'Please select a time')
      .required('Time is required'),
    selectedSite: Yup.string()
      .notOneOf(['', 'Select Site'], 'Please select a site')
      .required('Site is required'),
    selectedTable: Yup.string()
      .notOneOf(['', 'Select Tables'], 'Please select a table')
      .required('Table is required'),
    guestName: Yup.string()
      .min(2, 'Guest name must be at least 2 characters')
      .required('Guest name is required'),
  });

  const formik = useFormik({
    initialValues: {
      partySize: null,
      selectedDate: new Date(),
      selectedTime: '',
      selectedSite: '',
      selectedTable: '',
      guestName: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      // Convert time format from "12 PM" to "12:00"
      const timeMatch = values.selectedTime.match(/(\d+)\s*(AM|PM)/i);
      let hour = parseInt(timeMatch?.[1] || "0");
      const period = timeMatch?.[2]?.toUpperCase();

      // Convert to 24-hour format
      if (period === "PM" && hour < 12) hour += 12;
      if (period === "AM" && hour === 12) hour = 0;

      const timeFormatted = `${hour.toString().padStart(2, '0')}:00`;

      // Create payload for API
      const payload = {
        site: values.selectedSite,
        table: values.selectedTable,
        fullName: values.guestName,
        partySize: values.partySize,
        reserveDate: values.selectedDate.toISOString(),
        reserveTime: timeFormatted,
        userId: localStorage.getItem("userId") || ""
      };

      // Call the API
      const response = await createTableOperator(payload);

      if ('data' in response) {
        // Reset form
        formik.resetForm();

        // Show success message with SweetAlert
        Swal.fire({
          title: 'Success!',
          text: 'Reservation created successfully',
          icon: 'success',
          confirmButtonColor: '#FF5C00'
        });
        onContinue();
      }
    },
  });

  // Filter tables based on selected site - using location._id to match with site _id
  const filteredTables = tables?.filter(table =>
    table.location && table.location._id === formik.values.selectedSite
  ) || [];

  // Custom handlers to prevent selecting empty options after a valid selection
  const handleSiteChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value !== '' || formik.values.selectedSite === '') {
      formik.setFieldValue('selectedSite', value);
      // Reset table selection when site changes
      formik.setFieldValue('selectedTable', '');
    }
  };

  const handleTableChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value !== '' || formik.values.selectedTable === '') {
      formik.setFieldValue('selectedTable', value);
    }
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value !== '' || formik.values.selectedTime === '') {
      formik.setFieldValue('selectedTime', value);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const generateWeekDays = (startDate: Date) => {
    const days = [];
    const start = startOfWeek(startDate, { weekStartsOn: 0 });
    for (let i = 0; i < 7; i++) {
      days.push(addDays(start, i));
    }
    return days;
  };

  const weekDays = generateWeekDays(currentDate);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="bg-black/50 absolute inset-0" />
      <div ref={modalRef} className="relative bg-white rounded-lg w-[700px] max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">New Reservation</h2>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
              className="hover:bg-gray-100 p-1 rounded-full cursor-pointer transition-colors"
            >
              <IoClose size={24} />
            </button>
          </div>

          <form onSubmit={formik.handleSubmit}>
            {/* Party Size Selection */}
            <div className="mb-6">
              <label className="block text-gray-600 mb-2 text-sm">Select party size</label>
              <div className="grid grid-cols-8 gap-1 bg-gray-100 rounded-full p-1">
                {partySizes.map((size) => (
                  <button
                    type="button"
                    key={size}
                    className={`py-2 rounded-full text-center cursor-pointer transition-all ${formik.values.partySize === size
                      ? 'bg-orange-100 text-orange-500 font-medium'
                      : 'hover:bg-gray-200 text-gray-700'
                      }`}
                    onClick={() => formik.setFieldValue('partySize', size)}
                  >
                    {size}
                  </button>
                ))}
              </div>
              {formik.touched.partySize && formik.errors.partySize ? (
                <div className="text-red-500 text-xs mt-1">{formik.errors.partySize}</div>
              ) : null}
            </div>

            {/* Site and Table Selection in one row */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <label className="block text-gray-600 mb-2 text-sm">Site</label>
                <div className="relative">
                  <select
                    className={`w-full p-3 border ${formik.touched.selectedSite && formik.errors.selectedSite ? 'border-red-500' : 'border-gray-200'} rounded-lg appearance-none bg-white focus:outline-none focus:border-orange-500 transition-colors`}
                    value={formik.values.selectedSite}
                    onChange={handleSiteChange}
                    onBlur={formik.handleBlur}
                    name="selectedSite"
                  >
                    <option value="" disabled={formik.values.selectedSite !== ''}>Select Site</option>
                    {sites && sites.map(site => (
                      <option key={site._id} value={site._id}>{site.siteName}</option>
                    ))}
                  </select>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
                {formik.touched.selectedSite && formik.errors.selectedSite ? (
                  <div className="text-red-500 text-xs mt-1">{formik.errors.selectedSite}</div>
                ) : null}
              </div>
              <div className="flex-1">
                <label className="block text-gray-600 mb-2 text-sm">Tables</label>
                <div className="relative">
                  <select
                    className={`w-full p-3 border ${formik.touched.selectedTable && formik.errors.selectedTable ? 'border-red-500' : 'border-gray-200'} rounded-lg appearance-none bg-white focus:outline-none focus:border-orange-500 transition-colors`}
                    value={formik.values.selectedTable}
                    onChange={handleTableChange}
                    onBlur={formik.handleBlur}
                    name="selectedTable"
                  >
                    <option value="" disabled={formik.values.selectedTable !== ''}>Select Tables</option>
                    {filteredTables.map(table => (
                      <option key={table._id} value={table._id}>{table.tableName}</option>
                    ))}
                  </select>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
                {formik.touched.selectedTable && formik.errors.selectedTable ? (
                  <div className="text-red-500 text-xs mt-1">{formik.errors.selectedTable}</div>
                ) : null}
              </div>
            </div>

            {/* Date Selection */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <label className="text-gray-600 text-sm">Select Date</label>
                <div className="flex items-center gap-4">
                  <button
                    type="button"
                    onClick={() => setCurrentDate(subDays(currentDate, 7))}
                    className="hover:bg-gray-100 p-1 rounded transition-colors"
                  >
                    <IoIosArrowBack className="text-gray-600" />
                  </button>
                  <span className="font-medium text-sm">{format(currentDate, 'MMMM yyyy')}</span>
                  <button
                    type="button"
                    onClick={() => setCurrentDate(addDays(currentDate, 7))}
                    className="hover:bg-gray-100 p-1 rounded transition-colors"
                  >
                    <IoIosArrowForward className="text-gray-600" />
                  </button>
                </div>
              </div>
              <div className="bg-gray-100 rounded-2xl p-1  ">
                <div className="grid grid-cols-7 gap-1">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                    <div key={day} className="text-center text-xs text-gray-500 py-1">
                      {day}
                    </div>
                  ))}
                  {weekDays.map((date, index) => {
                    const isPastDate = isBefore(startOfDay(date), today);
                    return (
                      <button
                        type="button"
                        key={index}
                        disabled={isPastDate}
                        className={`py-2 rounded-xl text-center text-sm transition-all 
                          ${format(formik.values.selectedDate, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
                            ? 'bg-orange-100 text-orange-500 font-medium'
                            : isPastDate
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'hover:bg-gray-200 cursor-pointer text-gray-700'
                          }`}
                        onClick={() => {
                          if (!isPastDate) {
                            formik.setFieldValue('selectedDate', date);
                          }
                        }}
                      >
                        {format(date, 'd')}
                      </button>
                    );
                  })}
                </div>
              </div>
              {formik.touched.selectedDate && formik.errors.selectedDate ? (
                <div className="text-red-500 text-xs mt-1">{String(formik.errors.selectedDate)}</div>
              ) : null}
            </div>

            {/* Guest Name and Time Selection in one row */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <label className="block text-gray-600 mb-2 text-sm">Guest Name</label>
                <input
                  type="text"
                  className={`w-full p-3 border ${formik.touched.guestName && formik.errors.guestName ? 'border-red-500' : 'border-gray-200'} rounded-lg focus:outline-none focus:border-orange-500 transition-colors`}
                  placeholder="Enter guest name"
                  value={formik.values.guestName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  name="guestName"
                />
                {formik.touched.guestName && formik.errors.guestName ? (
                  <div className="text-red-500 text-xs mt-1">{formik.errors.guestName}</div>
                ) : null}
              </div>
              <div className="flex-1">
                <label className="block text-gray-600 mb-2 text-sm">Select Time</label>
                <div className="relative">
                  <select
                    className={`w-full p-3 border ${formik.touched.selectedTime && formik.errors.selectedTime ? 'border-red-500' : 'border-gray-200'} rounded-lg appearance-none bg-white focus:outline-none focus:border-orange-500 transition-colors`}
                    value={formik.values.selectedTime}
                    onChange={handleTimeChange}
                    onBlur={formik.handleBlur}
                    name="selectedTime"
                  >
                    <option value="" disabled={formik.values.selectedTime !== ''}>Select time</option>
                    {timeOptions.map((time) => (
                      <option key={time} value={time}>
                        {time}
                      </option>
                    ))}
                  </select>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
                {formik.touched.selectedTime && formik.errors.selectedTime ? (
                  <div className="text-red-500 text-xs mt-1">{formik.errors.selectedTime}</div>
                ) : null}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4">
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  onClose();
                }}
                className="flex-1 py-3 border cursor-pointer border-gray-200 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 py-3 cursor-pointer bg-[#FF5C00] text-white rounded-lg hover:bg-orange-600 transition-colors font-medium"
                disabled={isLoading}
              >
                {isLoading ? 'Creating...' : 'Continue'}
              </button>
            </div>

            {error && (
              <div className="mt-3 text-red-500 text-sm">
                Failed to create reservation. Please try again.
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

export default NewReservationModal;