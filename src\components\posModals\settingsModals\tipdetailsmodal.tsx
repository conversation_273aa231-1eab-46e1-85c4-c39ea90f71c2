import React from "react";
import CustomModal from "../../CustomModal";
import { format } from "date-fns";

interface TipDetailsModalProps {
    isOpen: boolean;
    onClose: () => void;
    tipData: {
        _id: string;
        OrderNumber: string;
        operatorName: string;
        grandTotal: number;
        Tips: number;
        EmployeeName: string;
        createdAt: string;
    } | null;
}

const TipDetailsModal: React.FC<TipDetailsModalProps> = ({
    isOpen,
    onClose,
    tipData,
}) => {
    if (!tipData) return null;

    const footer = (
        <div className="flex justify-end">
            <button
                onClick={onClose}
                className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors hover:bg-orange hover:text-white"
            >
                Close
            </button>
        </div>
    );

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            title="Tip Details"
            width="max-w-2xl"
            footer={footer}
        >
            <div className="p-6">
                <div className="space-y-6">
                    {/* Order Information */}
                    <div className="border-b border-gray-200 pb-4">
                        <h3 className="text-lg font-semibold mb-3">Order Information</h3>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <p className="text-gray-500 text-sm">Order Number</p>
                                <p className="font-medium">{tipData.OrderNumber}</p>
                            </div>
                            <div>
                                <p className="text-gray-500 text-sm">Date</p>
                                <p className="font-medium">
                                    {format(new Date(tipData.createdAt), "MMM dd, yyyy")}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Amount Details */}
                    <div className="border-b border-gray-200 pb-4">
                        <h3 className="text-lg font-semibold mb-3">Amount Details</h3>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <p className="text-gray-500 text-sm">Order Total</p>
                                <p className="font-medium text-lg">${tipData.grandTotal.toFixed(2)}</p>
                            </div>
                            <div>
                                <p className="text-gray-500 text-sm">Tip Amount</p>
                                <p className="font-medium text-lg text-green-600">
                                    ${tipData.Tips.toFixed(2)}
                                </p>
                            </div>
                        </div>
                        <div className="mt-3">
                            <p className="text-gray-500 text-sm">Tip Percentage</p>
                            <p className="font-medium">
                                {((tipData.Tips / tipData.grandTotal) * 100).toFixed(1)}%
                            </p>
                        </div>
                    </div>

                    {/* Personnel Information */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Personnel Information</h3>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <p className="text-gray-500 text-sm">Operator</p>
                                <p className="font-medium">{tipData.operatorName}</p>
                            </div>
                            <div>
                                <p className="text-gray-500 text-sm">Employee</p>
                                <p className="font-medium">{tipData.EmployeeName || "N/A"}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default TipDetailsModal;