@import "tailwindcss";

@import url('https://fonts.google.com/share?selection.family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

body {
  font-family: 'Roboto', sans-serif;
  overflow-x: hidden;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 0.5px;
  /* Thin width */
  height: 4px;
  /* For horizontal scroll */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Mobile Invoice Modal Animation */
@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Custom z-index for Tailwind */
.z-25 {
  z-index: 25;
}

/* Floating button pulse animation */
@keyframes float-pulse {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  }
}

.animate-float-pulse {
  animation: float-pulse 2s ease-in-out infinite;
}