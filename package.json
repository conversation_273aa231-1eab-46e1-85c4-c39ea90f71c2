{"name": "patronworks-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@reduxjs/toolkit": "^2.8.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "lucide-react": "^0.510.0", "react": "^19.1.0", "react-date-range": "^2.0.1", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "react-spinners": "^0.17.0", "react-to-print": "^3.1.0", "react-toastify": "^11.0.5", "react-tooltip": "^5.28.1", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "sweetalert2": "^11.21.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.7", "@types/react": "^19.1.2", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}