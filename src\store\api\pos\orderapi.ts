import { baseApi } from '../baseApi';

export interface XReportParams {
    userId: string;
    employeeId?: string;
    customerId?: string;
    reportType: 'Daily' | 'Weekly' | 'Monthly' | 'date';
    paymentIds?: string[];
    status: 'payment' | 'customers';
    startDate?: string;
    endDate?: string;
}

export interface XReportResponse {
    breakdown: Array<{
        totalOrders: number;
        totalRevenue: number;
        totalTax: number;
        paymentType: string;
        customerName?: string; // For customer breakdown
    }>;
    orders: Array<{
        _id: string;
        createdAt: string;
        totalTax: number;
        paymentType: string;
        totalPrice: number;
        customerName?: string; // Customer name for customer reports
        customerInfo?: {
            name: string;
            id: string;
        };
    }>;
    summary: {
        totalOrders: number;
        totalRevenue: number;
        totalTax: number;
    };
    taxReport: Array<{
        totalTax: number;
        taxName: string;
    }>;
}

export interface LastWeekOrdersParams {
    userId: string;
    startDate?: string;
    endDate?: string;
}

export const orderApi = baseApi.injectEndpoints({
    endpoints: (builder) => ({
        getOrders: builder.query<any[], string>({
            query: (userId) => `/orderitem?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getLastWeekOrders: builder.query<any[], string>({
            query: (userId) => `/orderitem/lastWeek?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getLastWeekOrdersWithDateRange: builder.query<any[], LastWeekOrdersParams>({
            query: (params) => {
                let queryString = `/lastweek-orders?userId=${params.userId}`;

                if (params.startDate) {
                    queryString += `&startDate=${encodeURIComponent(params.startDate)}`;
                }
                if (params.endDate) {
                    queryString += `&endDate=${encodeURIComponent(params.endDate)}`;
                }

                return queryString;
            },
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getOrdersWithRefundData: builder.query<any[], string>({
            query: (userId) => `/orderitem/withRefundData?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getDueOrders: builder.query<any[], string>({
            query: (userId) => `/dueOrders?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any) => {
                console.log("Raw API response:", response);

                if (Array.isArray(response)) {
                    return response;
                }

                if (response && response.data && Array.isArray(response.data)) {
                    return response.data;
                }

                if (response && typeof response === 'object' && response._id) {
                    return [response];
                }

                return [];
            },
        }),
        getLastOrder: builder.query<any, string>({
            query: (userId) => `/orderitem/lastOrder?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any) => response,
        }),
        getPayments: builder.query<any[], string>({
            query: (userId) => `/payment?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getXReport: builder.query<XReportResponse, XReportParams>({
            query: (params) => {
                let queryString = `/X-report?userId=${params.userId}`;

                if (params.employeeId) {
                    queryString += `&employeeId=${params.employeeId}`;
                }
                if (params.customerId) {
                    queryString += `&customerId=${params.customerId}`;
                }
                queryString += `&reportType=${params.reportType}`;
                if (params.paymentIds && params.paymentIds.length > 0) {
                    queryString += `&paymentIds=${params.paymentIds.join(',')}`;
                }
                queryString += `&status=${params.status}`;
                if (params.startDate && params.endDate) {
                    queryString += `&startDate=${params.startDate}&endDate=${params.endDate}`;
                }

                return queryString;
            },
            providesTags: ['pos'],
            transformResponse: (response: any) => response,
        }),
        getParentCategories: builder.query<any[], string>({
            query: (userId) => `/parentcategory?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getTaxes: builder.query<any[], string>({
            query: (userId) => `/tax?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getModifiers: builder.query<any[], string>({
            query: (userId) => `/modifiers?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getCoupons: builder.query<any[], string>({
            query: (userId) => `/coupens?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getTableOperator: builder.query<any[], string>({
            query: (userId) => `/table-operator?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),

        // ✅ New POST endpoint for creating an order item
        createOrderItem: builder.mutation<any, { body: any }>({
            query: ({ body }) => ({
                url: `/orderitem`,
                method: 'POST',
                body,
            }),
            invalidatesTags: ['pos'],
        }),

        updateOrderItem: builder.mutation<any, { id: string; body: any }>({
            query: ({ id, body }) => ({
                url: `/orderitem/${id}`,
                method: 'PUT',
                body,
            }),
            invalidatesTags: ['pos'],
        }),
    }),
    overrideExisting: false,
});

export const {
    useGetOrdersQuery,
    useGetLastWeekOrdersQuery,
    useGetLastWeekOrdersWithDateRangeQuery,
    useGetOrdersWithRefundDataQuery,
    useGetDueOrdersQuery,
    useGetLastOrderQuery,
    useGetPaymentsQuery,
    useGetXReportQuery,
    useGetParentCategoriesQuery,
    useGetTaxesQuery,
    useGetModifiersQuery,
    useGetCouponsQuery,
    useGetTableOperatorQuery,
    useCreateOrderItemMutation,
    useUpdateOrderItemMutation,
} = orderApi;