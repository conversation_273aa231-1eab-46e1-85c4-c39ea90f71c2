import React, { useState, useEffect } from "react";
import { Trash2, Edit,X  } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { useGetProductsQuery } from "../../../store/api/menuitemApi";
import { 
  useAddModifiersMutation, 
  useGetModifiersByUserQuery, 
  useUpdateModifierMutation,
  // Property as PropertyType
} from "../../../store/api/modifier";
import Swal from "sweetalert2";

// Define types
interface Property {
  name: string;
  totalQuantity: number | string;
  price: number | string;
  _id?: string;
}

interface Modifier {
  id: string;
  name: string;
  properties: Property[];
  isEditing: boolean;
  _id?: string;
}

const Modifiers: React.FC = () => {
  // Main state for the form with typed state
  const [menuItem, setMenuItem] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);
  const [modifiers, setModifiers] = useState<Modifier[]>([]);
  const [nextId, setNextId] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [isMenuItemsOpen, setIsMenuItemsOpen] = useState(false);
const [selectedMenuItem, setSelectedMenuItem] = useState<any>(null);

  const userId = localStorage.getItem("userId") || "";
  const { data: products = [] } = useGetProductsQuery(userId);
  const { data: modifiersData = [] } = useGetModifiersByUserQuery();

  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    if (id && modifiersData.length > 0) {
      const modifierToEdit = modifiersData.find(mod => mod._id === id);

      console.log("modifierToEdit", modifierToEdit);
      
      if (modifierToEdit) {
        setIsEditMode(true);
        setIsActive(modifierToEdit.isActive);
        
        // Set the menu item
        if (modifierToEdit.productId && modifierToEdit.productId._id) {
          setMenuItem(modifierToEdit.productId._id);
        }
        
        // Transform the backend modifier format to our component format
        if (modifierToEdit.Modifier && modifierToEdit.Modifier.length > 0) {
          const transformedModifiers = modifierToEdit.Modifier.map((mod, index) => ({
            id: `mod-${index + 1}`,
            name: mod.name,
            properties: mod.properties?.map(prop => ({
              name: prop.name,
              totalQuantity: prop.totalQuantity || 0,
              price: prop.price,
              _id: prop._id
            })) || [],
            isEditing: false,
            _id: mod._id
          }));
          
          setModifiers(transformedModifiers);
          setNextId(transformedModifiers.length + 1);
        }
      }
    }
  }, [id, modifiersData]);

const handleMenuItemChange = (e: React.ChangeEvent<HTMLSelectElement> | { target: { value: string } }) => {
  const selectedId = e.target.value;
  const product = products.find(p => p.id === selectedId);
  setSelectedMenuItem(product || null);

  setMenuItem(selectedId);

  
};
 
  const toggleActive = (): void => {
    setIsActive(!isActive);
  };

  // Add a new modifier
  const handleAddModifier = (): void => {
    const newModifier: Modifier = {
      id: `mod-${nextId}`,
      name: "",
      properties: [],
      isEditing: true,
    };

    setModifiers([...modifiers, newModifier]);
    setNextId(nextId + 1);
  };


  const handleModifierNameChange = (
    modifierId: string,
    newName: string
  ): void => {
    const updatedModifiers = modifiers.map((mod) =>
      mod.id === modifierId ? { ...mod, name: newName } : mod
    );

    setModifiers(updatedModifiers);
  };

  // Add a property to a modifier
  const handleAddProperty = (modifierId: string): void => {
    const updatedModifiers = modifiers.map((mod) => {
      if (mod.id === modifierId) {
        return {
          ...mod,
          properties: [
            ...mod.properties,
            { name: "", totalQuantity: "", price: "" },
          ],
        };
      }
      return mod;
    });
    setModifiers(updatedModifiers);
  };

  // Update property values
  const handlePropertyChange = (
    modifierId: string,
    propertyIndex: number,
    field: keyof Property,
    value: string | number
  ): void => {
    const updatedModifiers = modifiers.map((mod) => {
      if (mod.id === modifierId) {
        const updatedProperties = [...mod.properties];

        // Convert value to appropriate type based on field
        if (field === "name") {
          updatedProperties[propertyIndex][field] = value as string;
        } else {
          // For quantity and price, ensure we have a number
          updatedProperties[propertyIndex][field] =
            typeof value === "string" ? value : value.toString();
        }

        return {
          ...mod,
          properties: updatedProperties,
        };
      }
      return mod;
    });

    setModifiers(updatedModifiers);
  };

  const [addModifiers] = useAddModifiersMutation();
  const [updateModifier] = useUpdateModifierMutation();

  // Delete a property
  const handleDeleteProperty = (
    modifierId: string,
    propertyIndex: number
  ): void => {
    const updatedModifiers = modifiers.map((mod) => {
      if (mod.id === modifierId) {
        const updatedProperties = mod.properties.filter(
          (_, i) => i !== propertyIndex
        );
        return {
          ...mod,
          properties: updatedProperties,
        };
      }
      return mod;
    });

    setModifiers(updatedModifiers);
  };

  // Save a specific modifier
  const handleSaveModifier = (modifierId: string): void => {
    const updatedModifiers = modifiers.map((mod) =>
      mod.id === modifierId ? { ...mod, isEditing: false } : mod
    );

    setModifiers(updatedModifiers);
  };

  // Edit a specific modifier
  const handleEditModifier = (modifierId: string): void => {
    const updatedModifiers = modifiers.map((mod) =>
      mod.id === modifierId ? { ...mod, isEditing: true } : mod
    );

    setModifiers(updatedModifiers);
  };

  // Delete a modifier
  const handleDeleteModifier = (modifierId: string): void => {
    const updatedModifiers = modifiers.filter((mod) => mod.id !== modifierId);
    setModifiers(updatedModifiers);
  };

  // Discard all changes
  const handleDiscardAllChanges = (): void => {
    if (isEditMode && id && modifiersData.length > 0) {
      // Revert to original data if in edit mode
      const modifierToEdit = modifiersData.find(mod => mod._id === id);
      
      if (modifierToEdit) {
        setIsActive(modifierToEdit.isActive);
        
        if (modifierToEdit.productId && modifierToEdit.productId._id) {
          setMenuItem(modifierToEdit.productId._id);
        }
        
        if (modifierToEdit.Modifier && modifierToEdit.Modifier.length > 0) {
          const transformedModifiers = modifierToEdit.Modifier.map((mod, index) => ({
            id: `mod-${index + 1}`,
            name: mod.name,
            properties: mod.properties?.map(prop => ({
              name: prop.name,
              totalQuantity: prop.totalQuantity || 0,
              price: prop.price,
              _id: prop._id
            })) || [],
            isEditing: false,
            _id: mod._id
          }));
          setModifiers(transformedModifiers);
        }
      }
    } else {
      setModifiers([]);
    }
  };


  const resetForm = (): void => {
    setModifiers([]);
    setMenuItem("");
    setIsActive(false);
    setNextId(1);
  };

  const handleSaveAllModifiers = async (): Promise<void> => {
    if (!menuItem) {
      Swal.fire({
        title: 'Selection Required',
        text: 'Please select a menu item',
        icon: 'warning',
        confirmButtonColor: '#6366f1', // indigo color
      });
      return;
    }
  
    try {
      setIsLoading(true);
   
      const payload = {
        Modifier: modifiers.map(mod => ({
          name: mod.name,
          properties: mod.properties.map(prop => ({
            name: prop.name,
            totalQuantity: Number(prop.totalQuantity) || 0,
            price: Number(prop.price) || 0
          }))
        })),
        isActive,
        productId: menuItem,
        userId: localStorage.getItem('userId') || ""
      };
  
      
      let response;
      
      if (isEditMode && id) {
        response = await updateModifier({ 
          id, 
          data: payload 
        }).unwrap();
        
        if (response && typeof response === 'object' && 'message' in response) {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: response.message,
            confirmButtonColor: '#6366f1', 
          });
        }
      } else {
        // For create
        response = await addModifiers(payload).unwrap();
      }
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: isEditMode 
          ? 'Modifier updated successfully!' 
          : 'Modifier created successfully!',
        confirmButtonColor: '#6366f1',
      });
      // Reset form and navigate back
      resetForm();
      navigate("/admin/catalog/modifiers"); 
    } catch (error) {
      console.error("Error saving modifiers:", error);
      
      // Handle different error structures
      let errorMessage = "Failed to save modifiers";
      
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        if ('data' in error && error.data && typeof error.data === 'string') {
          errorMessage = error.data;
        } else if ('message' in error && typeof error.message === 'string') {
          errorMessage = error.message;
        }
      }
      
      alert(`Error: ${errorMessage}`);
    }finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="rounded-lg">
        {/* Header */}
        <div className="flex justify-between items-center bg-white m-4 p-3 rounded-2xl border border-gray-200 ">
          <div className="flex items-center">
            <button className="mr-2" type="button" onClick={() => navigate(-1)}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M15 18l-6-6 6-6" />
              </svg>
            </button>
            <h1 className="text-xl font-bold">{isEditMode ? "Edit" : "Create"} Modifiers</h1>
          </div>
          <div className="flex space-x-2">
            <button
              className={`bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded flex items-center justify-center ${
                isLoading ? "opacity-70 cursor-not-allowed" : ""
              }`}
              onClick={handleSaveAllModifiers}
              type="button"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Saving...
                </>
              ) : (
                `${isEditMode ? "Update" : "Save"} Modifier`
              )}
            </button>
            <button
              className="border border-orange-500 text-orange-500 px-4 py-2 rounded"
              onClick={handleDiscardAllChanges}
              type="button"
              disabled={isLoading}
            >
              Discard Changes
            </button>
          </div>
        </div>

        {/* Menu Item Details */}
        <div className="border border-gray-300 rounded-2xl bg-white m-4">
          <h2 className="font-bold text-lg mb-4 p-4 rounded-t-2xl bg-orange-50">
            Menu Item Details
          </h2>
         <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
  <div className="w-full md:w-1/2 p-4">
    <label className="block mb-2" htmlFor="menuItem">
      Select Menu Item
    </label>
    
    {/* Replace the native select with custom dropdown */}
    <div className="relative">
      <button
        type="button"
        className="w-full p-2 border border-gray-200 rounded-md bg-white flex justify-between items-center"
        onClick={() => setIsMenuItemsOpen(!isMenuItemsOpen)}
      >
        <span className="text-gray-500">
          {selectedMenuItem?.name || "Select Menu Item"}
        </span>
        <svg
          className="w-4 h-4 ml-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isMenuItemsOpen && (
        <div className="absolute left-0 right-0 mt-1 bg-white border border-orange-500 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
          <div className="p-2 border-b border-b-gray-200 flex justify-between items-center">
            <span className="text-sm font-medium">Select Menu Item</span>
            <X
              className="w-4 h-4 cursor-pointer text-gray-500"
              onClick={() => setIsMenuItemsOpen(false)}
            />
          </div>
          {products.map((product: any) => (
            <div
              key={product.id}
              className="p-2 hover:bg-orange-50 cursor-pointer border-l-4 border-transparent hover:border-orange-500"
              onClick={() => {
                setSelectedMenuItem(product);
                handleMenuItemChange({ target: { value: product.id } });
                setIsMenuItemsOpen(false);
              }}
            >
              <span>{product.name}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  </div>

  {/* Keep the rest of your permissions and button code */}
  <div>
    <div className="flex flex-col items-center space-y-2">
      <label>Permissions</label>
      <div className="flex items-center space-x-2">
        <button
          className={`relative inline-flex h-6 w-11 items-center rounded-full ${
            isActive ? "bg-orange-500" : "bg-gray-200"
          }`}
          onClick={toggleActive}
          type="button"
          aria-pressed={isActive}
          aria-label="Toggle active state"
          disabled={isLoading}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
              isActive ? "translate-x-6" : "translate-x-1"
            }`}
          />
        </button>
        <span>Active</span>
      </div>
    </div>
  </div>
   
  <button
    className={`bg-orange-500 m-5 hover:bg-orange-600 text-white px-4 py-2 rounded ${
      isLoading ? "opacity-70 cursor-not-allowed" : ""
    }`}
    onClick={handleAddModifier}
    type="button"
    disabled={isLoading}
  >
    Add Modifier
  </button>
</div>
        </div>

        {/* Modifiers Section */}
        <div className="bg-white rounded-2xl border border-gray-200 m-4">
          <h2 className="font-bold bg-orange-50 rounded-t-2xl p-4 text-lg mb-4">
            Modifiers
          </h2>

          {/* List all modifiers */}
          {modifiers.map((mod) => (
            <div key={mod.id} className="mb-6 p-4 rounded bg-white">
              {mod.isEditing ? (
                /* Editable Modifier */
                <>
                  <div className="mb-4">
                    <label
                      className="block mb-2"
                      htmlFor={`modifier-${mod.id}`}
                    >
                      Modifier Name
                    </label>
                    <div className="flex">
                      <input
                        type="text"
                        id={`modifier-${mod.id}`}
                    className="w-full border border-gray-200 rounded p-2 focus:border-orange-500 focus:outline-none"
                        value={mod.name}
                        onChange={(e) =>
                          handleModifierNameChange(mod.id, e.target.value)
                        }
                        placeholder="Enter modifier name"
                        disabled={isLoading}
                      />
                      <button
                        onClick={() => handleDeleteModifier(mod.id)}
                        className="ml-2"
                        type="button"
                        aria-label="Delete modifier"
                        disabled={isLoading}
                      >
                        <Trash2 className="text-red-500" size={20} />
                      </button>
                    </div>
                  </div>

                  {/* Properties */}
                  {mod.properties.length > 0 && (
                    <div className="grid grid-cols-3 gap-4 mb-2 font-semibold">
                      <div>Property Name</div>
                      <div>Property Quantity</div>
                      <div>Property Price</div>
                    </div>
                  )}

                  {mod.properties.map((prop, propIndex) => (
                    <div
                      key={propIndex}
                      className="grid grid-cols-3 gap-4 mb-2"
                    >
                      <input
                        type="text"
                       className="w-full border border-gray-200 rounded p-2 focus:border-orange-500 focus:outline-none"
                        value={prop.name}
                        onChange={(e) =>
                          handlePropertyChange(
                            mod.id,
                            propIndex,
                            "name",
                            e.target.value
                          )
                        }
                        placeholder="Property name"
                        aria-label={`Property ${propIndex + 1} name`}
                        disabled={isLoading}
                      />
                      <input
                        type="number"
                       className="w-full border border-gray-200 rounded p-2 focus:border-orange-500 focus:outline-none"
                        value={prop.totalQuantity}
                        onChange={(e) =>
                          handlePropertyChange(
                            mod.id,
                            propIndex,
                            "totalQuantity",
                            e.target.value
                          )
                        }
                        placeholder="Quantity"
                        min="0"
                        aria-label={`Property ${propIndex + 1} quantity`}
                        disabled={isLoading}
                      />
                      <div className="flex">
                        <input
                          type="number"
                         className="w-full border border-gray-200 rounded p-2 focus:border-orange-500 focus:outline-none"
                          value={prop.price}
                          onChange={(e) =>
                            handlePropertyChange(
                              mod.id,
                              propIndex,
                              "price",
                              e.target.value
                            )
                          }
                          placeholder="Price"
                          step="0.01"
                          min="0"
                          aria-label={`Property ${propIndex + 1} price`}
                          disabled={isLoading}
                        />
                        <button
                          onClick={() =>
                            handleDeleteProperty(mod.id, propIndex)
                          }
                          className="ml-2"
                          type="button"
                          aria-label={`Delete property ${propIndex + 1}`}
                          disabled={isLoading}
                        >
                          <Trash2 className="text-red-500" size={20} />
                        </button>
                      </div>
                    </div>
                  ))}

                  <div className="flex space-x-2 mt-4">
                    <button
                      className={`bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded ${
                        isLoading ? "opacity-70 cursor-not-allowed" : ""
                      }`}
                      onClick={() => handleAddProperty(mod.id)}
                      type="button"
                      disabled={isLoading}
                    >
                      Add Property
                    </button>
                    {mod.name && (
                      <button
                        className={`bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded ${
                          isLoading ? "opacity-70 cursor-not-allowed" : ""
                        }`}
                        onClick={() => handleSaveModifier(mod.id)}
                        type="button"
                        disabled={isLoading}
                      >
                        Save This Modifier
                      </button>
                    )}
                  </div>
                </>
              ) : (
                /* View-only Modifier */
                <>
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center">
                      <h3 className="font-bold">{mod.name}</h3>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditModifier(mod.id)}
                        type="button"
                        className="text-blue-500 hover:text-blue-700"
                        disabled={isLoading}
                      >
                        <Edit size={20} className="text-blue-500" />
                      </button>
                      <button
                        onClick={() => handleDeleteModifier(mod.id)}
                        type="button"
                        aria-label={`Delete modifier ${mod.name}`}
                        disabled={isLoading}
                      >
                        <Trash2 className="text-red-500" size={20} />
                      </button>
                    </div>
                  </div>

                  {mod.properties.length > 0 && (
                    <div className="grid grid-cols-3 gap-4 mb-2 font-semibold">
                      <div>Property Name</div>
                      <div>Quantity</div>
                      <div>Price</div>
                    </div>
                  )}

                  {mod.properties.map((prop, propIndex) => (
                    <div
                      key={propIndex}
                      className="grid grid-cols-3 gap-4 mb-2"
                    >
                      <div>{prop.name}</div>
                      <div>{prop.totalQuantity}</div>
                      <div>{prop.price}</div>
                    </div>
                  ))}
                </>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Modifiers;