import React, { useState } from "react";
import Categories from "../Categories";
import { IoIosArrowRoundBack } from "react-icons/io";
import ReservationModal from "./ReservationModal";
import NewReservationModal from "./NewReservationModal";
import GuestDetailsModal from "./GuestDetailsModal";
import RunningOrdersModal from "./RunningOrdersModal";
import { useNavigate } from "react-router-dom";

interface TableHeaderProps {
  onTabChange: (tab: "Tables" | "Reservation" | "Running Orders") => void;
  activeTab: "Tables" | "Reservation" | "Running Orders";
}

const TableHeader: React.FC<TableHeaderProps> = ({
  onTabChange,
  activeTab,
}) => {
  const navigate = useNavigate();
  const [isReservationModalOpen, setIsReservationModalOpen] = useState(false);
  const [isNewReservationModalOpen, setIsNewReservationModalOpen] =
    useState(false);
  const [isGuestDetailsModalOpen, setIsGuestDetailsModalOpen] = useState(false);
  const [isRunningOrdersModalOpen, setIsRunningOrdersModalOpen] = useState(false);

  const handleCategorySelect = (category: string) => {
    if (category === "Reservation") {
      setIsReservationModalOpen(true);
    } else if (category === "Running Orders") {
      setIsRunningOrdersModalOpen(true);
    } else {
      onTabChange(category as "Tables" | "Reservation" | "Running Orders");
    }
  };

  // Modified to handle both modals


  const handleCloseNewReservation = () => {
    setIsNewReservationModalOpen(false);
  };

  const handleContinueNewReservation = () => {
    setIsNewReservationModalOpen(false);
    setIsGuestDetailsModalOpen(true);
  };


  return (
    <>
      <div className="flex justify-between items-center px-4 border-b-2 border-[#E4E4E4]">
        <div className="flex items-center space-x-4">
          <span onClick={() => navigate(-1)} className="mr-15 cursor-pointer">
            <IoIosArrowRoundBack size={30} className="ml-10" />
          </span>
          <div className="flex items-center space-x-9 border-l border-[#E4E4E4] pl-5">
            <span className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
              Available
            </span>
            <span className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-orange-400 mr-2"></div>
              Reserved
            </span>
            <span className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-green-400 mr-2"></div>
              Billed
            </span>
            <span className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-yellow-400 mr-2"></div>
              Available Soon
            </span>
          </div>
        </div>

        <div className="flex space-x-2">
          <Categories
            selected={activeTab}
            onSelect={handleCategorySelect}
            categories={["Tables", "Reservation", "Running Orders"]}
          />
        </div>
      </div>

      <ReservationModal
        isOpen={isReservationModalOpen}
        onClose={() => setIsReservationModalOpen(false)}

      />

      <NewReservationModal
        isOpen={isNewReservationModalOpen}
        onClose={handleCloseNewReservation}
        onContinue={handleContinueNewReservation}
      />

      <GuestDetailsModal
        isOpen={isGuestDetailsModalOpen}
        onClose={() => setIsGuestDetailsModalOpen(false)}

      />

      <RunningOrdersModal
        isOpen={isRunningOrdersModalOpen}
        onClose={() => setIsRunningOrdersModalOpen(false)}
      />
    </>
  );
};

export default TableHeader;
