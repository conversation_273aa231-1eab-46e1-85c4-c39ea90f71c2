import { baseApi } from '../baseApi';

// Define interfaces for Table and Site


export const tableApi = baseApi.injectEndpoints({
    endpoints: (builder) => ({
        getTables: builder.query<any[], string>({
            query: (userId) => `/tables?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getSites: builder.query<any[], string>({
            query: (userId) => `/site?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        createTableOperator: builder.mutation<any, any>({
            query: (body) => ({
                url: '/table-operator',
                method: 'POST',
                body,
            }),
            invalidatesTags: ['pos'],
        }),
    }),
    overrideExisting: false,
});

// Export hooks
export const {
    useGetTablesQuery,
    useGetSitesQuery,
    useCreateTableOperatorMutation,
} = tableApi;