import { ChevronDown } from "lucide-react";

type Props = {
  categories: string[];
  selected?: string;
  onSelect: (category: string) => void;
};

const Categories: React.FC<Props> = ({ categories, selected, onSelect }) => {
  return (
    <div className="w-full h-full overflow-hidden max-w-full">
      <div
        className="flex overflow-x-auto scrollbar-hide gap-2 px-3 lg:px-5 py-4 mt-2 h-full"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          WebkitOverflowScrolling: 'touch'
        }}
      >
        <div className="flex gap-2 min-w-max items-center">
          {categories.map((cat) => (
            <button
              key={cat}
              onClick={() => onSelect(cat)}
              className={`px-3 lg:px-4 py-2 cursor-pointer hover:text-orange hover:font-bold hover:border-orange rounded-full whitespace-nowrap border text-sm flex items-center flex-shrink-0 transition-colors duration-200 ${selected === cat && cat === "Discount"
                ? "bg-white text-light-black border-gray-300"
                : selected === cat
                  ? "text-orange font-bold border-orange"
                  : "bg-white text-light-black border-gray-300"
                }`}
            >
              {cat}
              {cat === "Discount" && <ChevronDown className="ml-1" size={14} />}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Categories;
