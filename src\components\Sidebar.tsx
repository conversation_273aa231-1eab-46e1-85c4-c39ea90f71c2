import React, { useState, useEffect } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { MdOutlineRestaurantMenu } from "react-icons/md";
import { RiUserAddFill } from "react-icons/ri";
import { BsFillFileBarGraphFill } from "react-icons/bs";
import { IoSettings } from "react-icons/io5";
import { useGetParentCategoriesQuery } from "../store/api/parentCategoryApi";

type DropdownKey = "menu" | "reports" | "settings";

interface SidebarProps {
  isOpen: boolean;
  onModalOpen: (modalName: string) => void;
  onCategoriesLoaded?: (categories: any[]) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onModalOpen, onCategoriesLoaded }) => {
  const userId = localStorage.getItem("userId") || " ";
  const { data: parentCategories } = useGetParentCategoriesQuery(userId);
  const [isMenuOpen, setIsMenuOpen] = useState(true);
  const [openSection, setOpenSection] = useState<"reports" | "settings" | null>(null);
  const [menuItems, setMenuItems] = useState<string[]>([]);


  const [selectedMenuItem, setSelectedMenuItem] = useState<string>("All"); // Default to "All"

  useEffect(() => {
    if (parentCategories && onCategoriesLoaded) {
      onCategoriesLoaded(parentCategories);
    }
  }, [parentCategories, onCategoriesLoaded]);


  useEffect(() => {
    if (parentCategories) {
      // Start with "All" which should always be shown
      const allParentItems = ["All"];

      console.log("Parent categories:", parentCategories);

      // Add all parent categories to the menu
      parentCategories.forEach((parentCat: any) => {
        allParentItems.push(parentCat.name);
      });

      console.log("All parent items:", allParentItems);

      setMenuItems(allParentItems);
    } else {
      // If no parent categories, set default
      setMenuItems(["All"]);
    }
  }, [parentCategories]);

  const toggleMenu = () => {
    setIsMenuOpen(prev => !prev);
    // Close other sections when menu is opened
    if (!isMenuOpen) {
      setOpenSection(null);
    }
  };

  const toggleSection = (key: "reports" | "settings") => {
    // Close menu when opening reports or settings
    setIsMenuOpen(false);
    // Toggle the clicked section
    setOpenSection(current => current === key ? null : key);
  };

  // Handle menu item selection
  const handleMenuItemSelect = (item: string) => {
    setSelectedMenuItem(item);
    onModalOpen(item);
  };

  const renderDropdown = (
    label: string,
    icon: React.ReactNode,
    key: DropdownKey,
    items: string[]
  ) => {
    // Determine if this dropdown should use the menu state or section state
    const isMenuSection = key === "menu";
    const isOpen = isMenuSection ? isMenuOpen : openSection === key;
    const toggleFn = isMenuSection ? toggleMenu : () => toggleSection(key as "reports" | "settings");

    return (
      <div className=" py-2">
        <div
          className="relative flex items-center justify-between text-black cursor-pointer overflow-auto"
          onClick={toggleFn}
        >
          <div className="flex items-center gap-2">
            <span className="text-orange-600">{icon}</span>
            <span>{label}</span>
          </div>
          {isOpen ? (
            <ChevronUp className="text-orange" size={23} />
          ) : (
            <ChevronDown className="text-orange" size={23} />
          )}
        </div>
        {isOpen && (
          <div className="ml-4 mt-3 text-sm space-y-1">
            {items.map((item) => {

              const isSelected = key === "menu" && selectedMenuItem === item;

              return (
                <div
                  key={item}
                  className={`cursor-pointer text-sm px-3 py-3 rounded-md transition-colors ${isSelected
                    ? 'bg-gradient-to-r from-red-400 to-orange-400 text-white'
                    : 'hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white'
                    }`}
                  onClick={() => key === "menu" ? handleMenuItemSelect(item) : onModalOpen(item)}
                >
                  {item}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  // Use default menu items if API data is not available
  const displayMenuItems = menuItems.length > 0
    ? menuItems
    : ["All", "Fast Food", "Pizza hut spagetti", "Saad Foods", "Course", "Cold Drinks"];

  return (
    <div
      className={`
        fixed top-[64px] left-0
        w-[280px]
        bg-white shadow-md
        h-[calc(100vh-64px)]
        overflow-y-auto scrollbar-hide py-6 mt-3
        border-r border-[#E4E4E4]
        pt-4 px-4 flex flex-col space-y-2
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        z-30
      `}
    >
      {renderDropdown("Menu", <MdOutlineRestaurantMenu size={25} />, "menu", displayMenuItems)}

      <div>
        <button
          className="group w-full flex items-center justify-start gap-2  text-black cursor-pointer bg-white rounded-md hover:bg-gradient-to-r hover:from-red-400 hover:to-orange-400 hover:text-white transition-all duration-200 py-2"
          onClick={() => onModalOpen("Add Customer")}
        >
          <span className="text-orange-600 group-hover:text-white transition-colors duration-200">
            <RiUserAddFill size={25} />
          </span>
          Add Customer
        </button>
      </div>

      {renderDropdown(
        "Reports",
        <BsFillFileBarGraphFill className="text-orange" size={25} />,
        "reports",
        [
          "Purchase History",
          "Bill Demonstration",
          "Cashier",
          "Inventory Reports",
          "inventory Sales Reports",
          "Modifier Reports",
          "X Report-Sales Summary",
          "Z Report-Sales Summary",
          "New Member",
          "Employee Access Record",
          "Customer Balance",
          "Account Payment Report",
        ]
      )}

      {renderDropdown("Settings", <IoSettings size={25} />, "settings", [
        "Refundable Orders",
        "Time Clock",
        "Clock Register",
        "History",
        "Tips",
        "Load Recipts",
        "Point Adjustment",
        "Pay Account",
        "Receive Payment",
        "Value Refund Reason",
        "Print Last Receipt",
      ])}
    </div>
  );
};

export default Sidebar;