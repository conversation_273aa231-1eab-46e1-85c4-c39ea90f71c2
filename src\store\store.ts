import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'
import { baseApi } from './api/baseApi'
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist'
import storage from 'redux-persist/lib/storage' // defaults to localStorage for web
import { combineReducers } from 'redux'

// Import all your reducers
import authReducer from './slices/authSlice'
import parentCategoryReducer from './slices/parentCategorySlice';
import categoryReducer from './slices/CategorySlice';
import ingredientsCategoryReducer from './slices/ingredientsCategorySlice';
import holdOrderReducer from './slices/holdorder';
import ingredientsReducer from './slices/ingredientsSlice';
import supplierReducer from './slices/supplierSlice';
import UnitOfMeasurementReducer from './slices/unitOfMeasurementSlice';
import wastageReducer from './slices/wastagesSlice';
import customerReducer from './slices/CustomerSlice'
import employeeListReducer from './slices/employeeListSlice';
import administrationReducer from './slices/administrationSlice';
import paymentTypesReducer from './slices/paymentTypesSlice';
import taxReducer from './slices/taxSlice';
import customizationReducer from './slices/customizationSlice';
import emailMarketingsReducer from "./slices/emailMarketingslice"
import LoyaltyOffersReducer from "./slices/LoyaltySlice"
import coupensReducer from "./slices/coupensSlice"
import checkoutReducer from './slices/checkoutSlice';
import tableManagementReducer from './slices/TableManagementSlice';
import siteManagementReducer from './slices/siteManagementSlice';
import modifierReducer from "./slices/modifiersSlice"
import MenuItemsReducer from './slices/menuItemsSlice';
import cartReducer from './slices/cartSlice';
import posOrderReducer from "./slices/posOrderSlice"
import selectedTablesReducer from './slices/selectedTablesSlice'
import selectedCustomerReducer from './slices/selectedcustomer'

// Combine all reducers
const rootReducer = combineReducers({
  [baseApi.reducerPath]: baseApi.reducer,
  auth: authReducer,
  parentCategory: parentCategoryReducer,
  category: categoryReducer,
  ingredientsCategory: ingredientsCategoryReducer,
  ingredients: ingredientsReducer,
  supplier: supplierReducer,
  unitOfMeasurement: UnitOfMeasurementReducer,
  wastage: wastageReducer,
  customer: customerReducer,
  employeeList: employeeListReducer,
  administration: administrationReducer,
  paymentTypes: paymentTypesReducer,
  taxes: taxReducer,
  customizations: customizationReducer,
  emailMarketings: emailMarketingsReducer,
  loyaltyOffers: LoyaltyOffersReducer,
  coupens: coupensReducer,
  checkout: checkoutReducer,
  tableManagement: tableManagementReducer,
  siteManagement: siteManagementReducer,
  modifier: modifierReducer,
  menuItems: MenuItemsReducer,
  cart: cartReducer,
  posOrder: posOrderReducer,
  selectedTables: selectedTablesReducer,
  selectedCustomer: selectedCustomerReducer,
  holdOrder: holdOrderReducer,
})

// Persist config
const persistConfig = {
  key: 'root',
  version: 1,
  storage,
  whitelist: ['auth', 'cart', 'checkout', 'holdOrder'],// persisted reducers
  // Optional: you can add state reconciler if needed
}

const persistedReducer = persistReducer(persistConfig, rootReducer)

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) => {
    const middleware = getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    });
    // @ts-ignore - Ignoring type issues for CI build
    return middleware.concat(baseApi.middleware);
  },
  devTools: import.meta.env.NODE_ENV !== 'production',
})

setupListeners(store.dispatch)

export const persistor = persistStore(store)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch