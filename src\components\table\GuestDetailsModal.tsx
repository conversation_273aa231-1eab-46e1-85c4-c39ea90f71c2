import React, { useState } from 'react';
import CustomModal from '../CustomModal';

interface GuestDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;

}

const GuestDetailsModal: React.FC<GuestDetailsModalProps> = ({ isOpen, onClose, }) => {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [visitorNote, setVisitorNote] = useState('');
  const [takenBy, setTakenBy] = useState('');
  const [pagerNumber, setPagerNumber] = useState('');

  const tags = ['VIP', 'Birthday', 'Anniversary', 'Private Dining', 'First Time'];

  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const handleSubmit = () => {
    const formData = {
      fullName,
      email,
      phone,
      selectedTags,
      visitorNote,
      takenBy,
      pagerNumber
    };

    console.log(formData);
    onClose();
  };

  const footer = (
    <div className="flex gap-4">
      <button
        onClick={onClose}
        className="flex-1 py-3 border border-gray-200 rounded-lg hover:bg-gray-50"
      >
        Cancel
      </button>
      <button
        onClick={handleSubmit}
        className="flex-1 py-3 bg-[#FF5C00] text-white rounded-lg hover:bg-orange-600"
      >
        Add to reservation
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Guest Details"
      width="max-w-xl"
      footer={footer}
    >
      <div className="p-6 space-y-4">
        {/* Full Name */}
        <div>
          <label className="block text-gray-600 mb-2">Full Name</label>
          <input
            type="text"
            placeholder="Enter name"
            className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:border-orange-500"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
          />
        </div>

        {/* Email and Phone */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-gray-600 mb-2">Email</label>
            <input
              type="email"
              placeholder="Enter email address"
              className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:border-orange-500"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-gray-600 mb-2">Phone Number</label>
            <input
              type="tel"
              placeholder="Enter phone number"
              className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:border-orange-500"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
            />
          </div>
        </div>

        {/* Tags */}
        <div>
          <label className="block text-gray-600 mb-2">Tags</label>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <button
                key={tag}
                onClick={() => toggleTag(tag)}
                className={`px-4 py-2 rounded-full border ${selectedTags.includes(tag)
                  ? tag === 'VIP' || tag === 'Anniversary'
                    ? 'bg-orange-100 text-orange-500 border-orange-500'
                    : 'bg-gray-100 text-gray-700 border-gray-300'
                  : 'border-gray-200 hover:bg-gray-50'
                  }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>

        {/* Visitor Note */}
        <div>
          <label className="block text-gray-600 mb-2">Visitor Note</label>
          <textarea
            placeholder="Write reservation note here"
            className="w-full p-3 border border-gray-200 rounded-lg h-24 focus:outline-none focus:border-orange-500"
            value={visitorNote}
            onChange={(e) => setVisitorNote(e.target.value)}
          />
        </div>

        {/* Taken by and Pager Number */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-gray-600 mb-2">Taken by (Initials)</label>
            <input
              type="text"
              placeholder="Taken by"
              className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:border-orange-500"
              value={takenBy}
              onChange={(e) => setTakenBy(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-gray-600 mb-2">Pager Number</label>
            <input
              type="text"
              placeholder="Enter pager number"
              className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:border-orange-500"
              value={pagerNumber}
              onChange={(e) => setPagerNumber(e.target.value)}
            />
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default GuestDetailsModal;