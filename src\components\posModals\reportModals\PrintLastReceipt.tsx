import React, { useRef } from "react";
import CustomModal from "../../CustomModal";
import { Printer } from "lucide-react";
import { useGetLastOrderQuery } from "../../../store/api/pos/orderapi";
import PrintableReceipt from "./PrintableReceipt";
import type { OrderItem } from "./PrintableReceipt";
import { useReactToPrint } from "react-to-print";

interface PrintLastReceiptProps {
  isOpen: boolean;
  onClose: () => void;
}

const PrintLastReceipt: React.FC<PrintLastReceiptProps> = ({
  isOpen,
  onClose,
}) => {
  const printRef = useRef<HTMLDivElement>(null);
  const handlePrint = useReactToPrint({
    getContent: () => printRef.current,
    documentTitle: "Order Receipt",
    pageStyle: `@page { size: 424px 652px; margin: 0; } body { -webkit-print-color-adjust: exact; }`,
  });
  const userId = localStorage.getItem("userId") || " ";
  const { data, isLoading, error } = useGetLastOrderQuery(userId);

  // Extract order items from API data
  const orderItems: OrderItem[] =
    data?.product?.map((item: any) => ({
      name: item.name,
      qty: data.productWithQty.find((p: any) => p.productId === item._id)?.qty || 1,
      price: item.price,
      subtotal:
        item.price *
        (data.productWithQty.find((p: any) => p.productId === item._id)?.qty || 1),
    })) || [];

  // Extract order summary data from API
  const subTotal = data?.lineValue || 0;
  const surcharge = data?.surCharge || 0;
  const orderDiscount = 0; // Assuming no discount in the data structure
  const tax = data?.lineValueTax || 0;
  const billAmount = data?.grandTotal || 0;
  const paymentMethod = "Cash"; // Assuming cash payment

  const footer = (
    <div className="flex justify-between items-center">
      <div></div>
      <div className="flex space-x-4">
        <button
          onClick={onClose}
          className="px-10 py-2.5 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-100 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handlePrint}
          className="px-10 py-2.5 bg-black cursor-pointer text-white rounded-full hover:bg-gray-800 transition-colors flex items-center"
        >
          <Printer className="w-4 h-4 mr-2" />
          Reprint
        </button>
      </div>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Order Details"
      width="max-w-xl"
      footer={footer}
      zIndex={100}
    >
      {isLoading ? (
        <div className="flex justify-center py-8 items-center">
          <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
          <div className="ml-3 text-orange-500">Loading</div>
        </div>
      ) : error ? (
        <div className="text-center py-10 text-red-500">
          <p>Error loading order data. Please try again.</p>
        </div>
      ) : !data ? (
        <div className="text-center py-10">
          <p>No order data available.</p>
        </div>
      ) : (
        <div className="px-6 py-4">
          {/* Your modal content here, or just a summary */}
          <div className="mb-6">
            <table className="w-full border-separate border-spacing-y-4">
              <thead>
                <tr className="text-left text-gray-500 border-b border-gray-100">
                  <th className="pb-2 font-medium text-sm">ITEM NAME</th>
                  <th className="pb-2 font-medium text-sm text-center">QTY</th>
                  <th className="pb-2 font-medium text-sm text-right">PRICE</th>
                  <th className="pb-2 font-medium text-sm text-right">SUBTOTAL</th>
                </tr>
              </thead>
              <tbody>
                {orderItems.length > 0 ? (
                  orderItems.map((item, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-3 font-medium">{item.name}</td>
                      <td className="py-3 text-center">{item.qty}</td>
                      <td className="py-3 text-right">${item.price.toFixed(2)}</td>
                      <td className="py-3 text-right">${item.subtotal.toFixed(2)}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="py-3 text-center text-gray-500">
                      No items found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Hidden printable receipt */}
      <div style={{ display: "none" }}>
        <PrintableReceipt
          ref={printRef}
          orderItems={orderItems}
          subTotal={subTotal}
          surcharge={surcharge}
          orderDiscount={orderDiscount}
          tax={tax}
          billAmount={billAmount}
          paymentMethod={paymentMethod}
        />
      </div>
    </CustomModal>
  );
};

export default PrintLastReceipt;